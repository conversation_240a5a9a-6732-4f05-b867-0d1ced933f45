<?php
session_start();

// Vérifier si l'utilisateur est connecté
if (!isset($_SESSION['user_email'])) {
    header('HTTP/1.1 401 Unauthorized');
    echo json_encode(['success' => false, 'message' => 'Non autorisé']);
    exit;
}

header('Content-Type: application/json');

$ligne = $_GET['ligne'] ?? 'nord';

// Données simulées pour les KPI
$data = [
    'nord' => [
        [
            'date' => '2024-01-15',
            'H2O' => 2.1,
            'P2O5' => 45.2,
            'Al' => 0.8,
            'Granulo' => 85,
            'AcideLibre' => 1.2
        ],
        [
            'date' => '2024-01-16',
            'H2O' => 2.3,
            'P2O5' => 44.8,
            'Al' => 0.9,
            'Granulo' => 87,
            'AcideLibre' => 1.1
        ],
        [
            'date' => '2024-01-17',
            'H2O' => 2.0,
            'P2O5' => 45.5,
            'Al' => 0.7,
            'Granulo' => 86,
            'AcideLibre' => 1.3
        ],
        [
            'date' => '2024-01-18',
            'H2O' => 2.2,
            'P2O5' => 45.0,
            'Al' => 0.8,
            'Granulo' => 88,
            'AcideLibre' => 1.0
        ],
        [
            'date' => '2024-01-19',
            'H2O' => 2.4,
            'P2O5' => 44.6,
            'Al' => 0.9,
            'Granulo' => 85,
            'AcideLibre' => 1.2
        ],
        [
            'date' => '2024-01-20',
            'H2O' => 2.1,
            'P2O5' => 45.3,
            'Al' => 0.8,
            'Granulo' => 87,
            'AcideLibre' => 1.1
        ],
        [
            'date' => '2024-01-21',
            'H2O' => 2.3,
            'P2O5' => 44.9,
            'Al' => 0.7,
            'Granulo' => 86,
            'AcideLibre' => 1.3
        ]
    ],
    'sud' => [
        [
            'date' => '2024-01-15',
            'H2O' => 1.9,
            'P2O5' => 46.1,
            'Al' => 0.6,
            'Granulo' => 89,
            'AcideLibre' => 0.9
        ],
        [
            'date' => '2024-01-16',
            'H2O' => 2.1,
            'P2O5' => 45.8,
            'Al' => 0.7,
            'Granulo' => 90,
            'AcideLibre' => 0.8
        ],
        [
            'date' => '2024-01-17',
            'H2O' => 1.8,
            'P2O5' => 46.3,
            'Al' => 0.5,
            'Granulo' => 88,
            'AcideLibre' => 1.0
        ],
        [
            'date' => '2024-01-18',
            'H2O' => 2.0,
            'P2O5' => 46.0,
            'Al' => 0.6,
            'Granulo' => 91,
            'AcideLibre' => 0.7
        ],
        [
            'date' => '2024-01-19',
            'H2O' => 2.2,
            'P2O5' => 45.7,
            'Al' => 0.8,
            'Granulo' => 87,
            'AcideLibre' => 0.9
        ],
        [
            'date' => '2024-01-20',
            'H2O' => 1.9,
            'P2O5' => 46.2,
            'Al' => 0.6,
            'Granulo' => 89,
            'AcideLibre' => 0.8
        ],
        [
            'date' => '2024-01-21',
            'H2O' => 2.1,
            'P2O5' => 45.9,
            'Al' => 0.7,
            'Granulo' => 90,
            'AcideLibre' => 0.9
        ]
    ]
];

echo json_encode([
    'success' => true,
    'data' => $data[$ligne] ?? $data['nord']
]);
?>
