-- Base de données OCP KPI TSP - Version Simplifiée
-- Tout en un seul fichier SQL

-- 1. CRÉATION DES TABLES
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(10) NOT NULL DEFAULT 'user' CHECK (role IN ('admin', 'user')),
    nom VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE lignes_production (
    id SERIAL PRIMARY KEY,
    nom VARCHAR(50) NOT NULL UNIQUE,
    description TEXT
);

CREATE TABLE kpi_data (
    id SERIAL PRIMARY KEY,
    ligne_id INTEGER NOT NULL,
    date_mesure DATE NOT NULL,
    heure_mesure TIME,
    h2o DECIMAL(5,2) NOT NULL,
    p2o5_soluble DECIMAL(5,2) NOT NULL,
    al DECIMAL(5,2) NOT NULL,
    granulometrie DECIMAL(5,2) NOT NULL,
    acide_libre DECIMAL(5,2) NOT NULL,
    operateur VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ligne_id) REFERENCES lignes_production(id)
);

CREATE TABLE fichiers_imports (
    id SERIAL PRIMARY KEY,
    nom_fichier VARCHAR(255) NOT NULL,
    user_id INTEGER,
    date_import TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 2. INDEX ESSENTIELS
CREATE INDEX idx_kpi_data_ligne_date ON kpi_data(ligne_id, date_mesure);
CREATE INDEX idx_kpi_data_date ON kpi_data(date_mesure);

-- 3. DONNÉES DE BASE
INSERT INTO users (email, password, role, nom) VALUES
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'Admin'),
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 'Utilisateur');

INSERT INTO lignes_production (nom, description) VALUES
('Nord', 'Ligne de production Nord'),
('Sud', 'Ligne de production Sud');

-- 4. DONNÉES EXEMPLE
INSERT INTO kpi_data (ligne_id, date_mesure, heure_mesure, h2o, p2o5_soluble, al, granulometrie, acide_libre, operateur) VALUES
-- Ligne Nord
(1, '2024-01-20', '08:00:00', 2.1, 45.2, 0.8, 85.0, 1.2, 'Ahmed'),
(1, '2024-01-20', '14:00:00', 2.3, 44.8, 0.9, 87.0, 1.1, 'Ahmed'),
(1, '2024-01-20', '20:00:00', 2.0, 45.5, 0.7, 86.0, 1.3, 'Fatima'),
(1, '2024-01-21', '08:00:00', 2.2, 45.0, 0.8, 88.0, 1.0, 'Ahmed'),
(1, '2024-01-21', '14:00:00', 2.4, 44.6, 0.9, 85.0, 1.2, 'Ahmed'),
-- Ligne Sud
(2, '2024-01-20', '08:00:00', 1.9, 46.1, 0.6, 89.0, 0.9, 'Ahmed'),
(2, '2024-01-20', '14:00:00', 2.1, 45.8, 0.7, 90.0, 0.8, 'Ahmed'),
(2, '2024-01-20', '20:00:00', 1.8, 46.3, 0.5, 88.0, 1.0, 'Fatima'),
(2, '2024-01-21', '08:00:00', 2.0, 46.0, 0.6, 91.0, 0.7, 'Ahmed'),
(2, '2024-01-21', '14:00:00', 2.2, 45.7, 0.8, 87.0, 0.9, 'Ahmed');

-- 5. VUES ESSENTIELLES
CREATE VIEW v_dernieres_mesures AS
SELECT 
    lp.nom as ligne_nom,
    kd.*
FROM kpi_data kd
JOIN lignes_production lp ON kd.ligne_id = lp.id
WHERE (kd.ligne_id, kd.created_at) IN (
    SELECT ligne_id, MAX(created_at)
    FROM kpi_data
    GROUP BY ligne_id
);

CREATE VIEW v_moyennes_journalieres AS
SELECT 
    lp.nom as ligne_nom,
    kd.ligne_id,
    kd.date_mesure,
    ROUND(AVG(kd.h2o), 2) as avg_h2o,
    ROUND(AVG(kd.p2o5_soluble), 2) as avg_p2o5_soluble,
    ROUND(AVG(kd.al), 2) as avg_al,
    ROUND(AVG(kd.granulometrie), 2) as avg_granulometrie,
    ROUND(AVG(kd.acide_libre), 2) as avg_acide_libre,
    COUNT(*) as nb_mesures
FROM kpi_data kd
JOIN lignes_production lp ON kd.ligne_id = lp.id
GROUP BY kd.ligne_id, kd.date_mesure, lp.nom
ORDER BY kd.date_mesure DESC;

-- 6. REQUÊTES UTILES COMMENTÉES

-- Obtenir les dernières mesures
-- SELECT * FROM v_dernieres_mesures;

-- Obtenir les données d'une ligne spécifique
-- SELECT * FROM kpi_data WHERE ligne_id = 1 ORDER BY date_mesure DESC, heure_mesure DESC LIMIT 10;

-- Moyennes des 7 derniers jours
-- SELECT 
--     lp.nom as ligne,
--     ROUND(AVG(kd.h2o), 2) as avg_h2o,
--     ROUND(AVG(kd.p2o5_soluble), 2) as avg_p2o5,
--     COUNT(*) as nb_mesures
-- FROM kpi_data kd
-- JOIN lignes_production lp ON kd.ligne_id = lp.id
-- WHERE kd.date_mesure >= CURRENT_DATE - INTERVAL '7 days'
-- GROUP BY kd.ligne_id, lp.nom;

-- Évolution par jour
-- SELECT 
--     date_mesure,
--     ligne_id,
--     ROUND(AVG(h2o), 2) as avg_h2o,
--     ROUND(AVG(p2o5_soluble), 2) as avg_p2o5
-- FROM kpi_data 
-- WHERE date_mesure >= CURRENT_DATE - INTERVAL '30 days'
-- GROUP BY date_mesure, ligne_id
-- ORDER BY date_mesure DESC;
