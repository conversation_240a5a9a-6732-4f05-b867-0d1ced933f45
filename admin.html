<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Administration - OCP KPI TSP</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="admin-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="header-left">
                    <div class="header-logo">OCP</div>
                    <h1 class="header-title">Administration - Suivi KPI TSP</h1>
                </div>
                <div class="header-right">
                    <a href="dashboard.html" class="btn">
                        🏠 Dashboard
                    </a>
                    <a href="index.html" class="btn">
                        🚪 Déconnexion
                    </a>
                </div>
            </div>
        </header>

        <div class="main-content">
            <!-- Upload de fichier Excel -->
            <div class="upload-card">
                <h2 style="color: #065f46; margin-bottom: 1rem; display: flex; align-items: center;">
                    📊 Import de données Excel
                </h2>
                <p style="color: #059669; margin-bottom: 1.5rem;">
                    Importez vos fichiers Excel contenant les données KPI TSP
                </p>
                
                <div style="margin-bottom: 1rem;">
                    <label for="excel-file" style="display: block; margin-bottom: 0.5rem; color: #047857; font-weight: 500;">
                        Sélectionner un fichier Excel
                    </label>
                    <input type="file" id="excel-file" accept=".xlsx,.xls" class="file-input">
                </div>
                
                <div id="upload-status" style="margin: 1rem 0;"></div>
                
                <button onclick="handleFileUpload()" class="btn btn-primary" style="width: 100%;">
                    📤 Traiter le fichier
                </button>
            </div>

            <!-- Statistiques -->
            <div class="stats-grid">
                <div class="stat-card">
                    <h3 style="color: #065f46; margin-bottom: 0.5rem; display: flex; align-items: center;">
                        👥 Utilisateurs
                    </h3>
                    <div class="stat-value">24</div>
                    <div class="stat-label">Utilisateurs actifs</div>
                </div>
                
                <div class="stat-card">
                    <h3 style="color: #065f46; margin-bottom: 0.5rem; display: flex; align-items: center;">
                        📊 Fichiers traités
                    </h3>
                    <div class="stat-value">156</div>
                    <div class="stat-label">Ce mois-ci</div>
                </div>
                
                <div class="stat-card">
                    <h3 style="color: #065f46; margin-bottom: 0.5rem; display: flex; align-items: center;">
                        ⚙️ Système
                    </h3>
                    <div class="stat-value">99.9%</div>
                    <div class="stat-label">Disponibilité</div>
                </div>
            </div>

            <!-- Instructions -->
            <div class="upload-card">
                <h3 style="color: #065f46; margin-bottom: 1rem;">Format du fichier Excel</h3>
                <div style="color: #047857; font-size: 0.9rem;">
                    <p style="margin-bottom: 0.5rem;"><strong>Colonnes requises :</strong></p>
                    <ul style="margin-left: 1.5rem; line-height: 1.6;">
                        <li>Date</li>
                        <li>Ligne (Nord/Sud)</li>
                        <li>H2O (%)</li>
                        <li>P2O5 soluble dans l'eau (%)</li>
                        <li>Al (%)</li>
                        <li>Granulométrie</li>
                        <li>Acide libre (%)</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
