// Fonctions communes
function togglePassword() {
  const passwordInput = document.getElementById("password")
  const eyeIcon = document.getElementById("eyeIcon")

  if (passwordInput.type === "password") {
    passwordInput.type = "text"
    eyeIcon.textContent = "🙈"
  } else {
    passwordInput.type = "password"
    eyeIcon.textContent = "👁️"
  }
}

function showError(elementId, message) {
  const errorElement = document.getElementById(elementId)
  errorElement.textContent = message
  errorElement.classList.add("show")
}

function hideError(elementId) {
  const errorElement = document.getElementById(elementId)
  errorElement.classList.remove("show")
}

function showSpinner() {
  document.getElementById("loginText").style.display = "none"
  document.getElementById("spinner").style.display = "block"
  document.getElementById("loginBtn").disabled = true
}

function hideSpinner() {
  document.getElementById("loginText").style.display = "block"
  document.getElementById("spinner").style.display = "none"
  document.getElementById("loginBtn").disabled = false
}

// Gestion du formulaire de connexion
if (document.getElementById("loginForm")) {
  document.getElementById("loginForm").addEventListener("submit", (e) => {
    e.preventDefault()

    const email = document.getElementById("email").value
    const password = document.getElementById("password").value

    // Validation côté client
    hideError("emailError")
    hideError("loginError")

    if (!email.includes("@ocp")) {
      showError("emailError", "L'email doit contenir @ocp")
      return
    }

    showSpinner()

    // Envoi des données au serveur
    const formData = new FormData()
    formData.append("email", email)
    formData.append("password", password)

    fetch("login.php", {
      method: "POST",
      body: formData,
    })
      .then((response) => response.json())
      .then((data) => {
        hideSpinner()

        if (data.success) {
          window.location.href = data.redirect
        } else {
          showError("loginError", data.message)
        }
      })
      .catch((error) => {
        hideSpinner()
        showError("loginError", "Erreur de connexion au serveur")
        console.error("Error:", error)
      })
  })
}

// Fonctions pour le dashboard
function switchTab(tabName) {
  // Masquer tous les contenus d'onglets
  const tabContents = document.querySelectorAll(".tab-content")
  tabContents.forEach((content) => content.classList.remove("active"))

  // Désactiver tous les boutons d'onglets
  const tabButtons = document.querySelectorAll(".tab-button")
  tabButtons.forEach((button) => button.classList.remove("active"))

  // Activer l'onglet sélectionné
  document.getElementById(tabName + "-content").classList.add("active")
  document.querySelector(`[onclick="switchTab('${tabName}')"]`).classList.add("active")

  // Charger les données pour cet onglet
  loadDashboardData(tabName)
}

function loadDashboardData(ligne) {
  fetch(`get_data.php?ligne=${ligne}`)
    .then((response) => response.json())
    .then((result) => {
      if (result.success) {
        updateKPICards(result.data, ligne)
        updateCharts(result.data, ligne)
      }
    })
    .catch((error) => {
      console.error("Erreur lors du chargement des données:", error)
    })
}

function updateKPICards(data, ligne) {
  if (data.length === 0) return

  const latest = data[data.length - 1]
  const previous = data[data.length - 2] || latest

  // Calculer les moyennes
  const avgH2O = (data.reduce((sum, item) => sum + item.H2O, 0) / data.length).toFixed(2)
  const avgP2O5 = (data.reduce((sum, item) => sum + item.P2O5, 0) / data.length).toFixed(2)
  const avgAl = (data.reduce((sum, item) => sum + item.Al, 0) / data.length).toFixed(2)
  const avgGranulo = Math.round(data.reduce((sum, item) => sum + item.Granulo, 0) / data.length)
  const avgAcideLibre = (data.reduce((sum, item) => sum + item.AcideLibre, 0) / data.length).toFixed(2)

  // Mettre à jour les cartes KPI
  updateKPICard(`${ligne}-h2o`, latest.H2O, avgH2O, getTrend(latest.H2O, previous.H2O))
  updateKPICard(`${ligne}-p2o5`, latest.P2O5, avgP2O5, getTrend(latest.P2O5, previous.P2O5))
  updateKPICard(`${ligne}-al`, latest.Al, avgAl, getTrend(latest.Al, previous.Al))
  updateKPICard(`${ligne}-granulo`, latest.Granulo, avgGranulo, getTrend(latest.Granulo, previous.Granulo))
  updateKPICard(`${ligne}-acide`, latest.AcideLibre, avgAcideLibre, getTrend(latest.AcideLibre, previous.AcideLibre))
}

function updateKPICard(cardId, currentValue, avgValue, trend) {
  const card = document.getElementById(cardId)
  if (!card) return

  const valueElement = card.querySelector(".kpi-value")
  const trendElement = card.querySelector(".kpi-trend")

  if (valueElement) {
    valueElement.textContent = currentValue
  }

  if (trendElement) {
    const trendIcon = trend === "up" ? "↗️" : trend === "down" ? "↘️" : "➡️"
    trendElement.innerHTML = `${trendIcon} Moy J-1: ${avgValue}`
  }
}

function getTrend(current, previous) {
  if (current > previous) return "up"
  if (current < previous) return "down"
  return "stable"
}

function updateCharts(data, ligne) {
  // Ici vous pouvez intégrer une bibliothèque de graphiques comme Chart.js
  // Pour cet exemple, nous affichons juste un message
  const chartContainers = document.querySelectorAll(`#${ligne}-content .chart-container`)
  chartContainers.forEach((container) => {
    container.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #059669;">
                <p>Graphique pour ${ligne.toUpperCase()} - ${data.length} points de données</p>
            </div>
        `
  })
}

// Fonctions pour l'admin
function handleFileUpload() {
  const fileInput = document.getElementById("excel-file")
  const file = fileInput.files[0]

  if (!file) {
    alert("Veuillez sélectionner un fichier")
    return
  }

  const formData = new FormData()
  formData.append("excel_file", file)

  // Afficher le statut de traitement
  const statusDiv = document.getElementById("upload-status")
  statusDiv.innerHTML = '<p style="color: #059669;">Traitement en cours...</p>'

  fetch("upload.php", {
    method: "POST",
    body: formData,
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        statusDiv.innerHTML = `<p style="color: #059669;">${data.message}</p>`
        // Réinitialiser le formulaire
        fileInput.value = ""
      } else {
        statusDiv.innerHTML = `<p style="color: #dc2626;">${data.message}</p>`
      }
    })
    .catch((error) => {
      statusDiv.innerHTML = '<p style="color: #dc2626;">Erreur lors de l\'upload</p>'
      console.error("Error:", error)
    })
}

// Initialisation au chargement de la page
document.addEventListener("DOMContentLoaded", () => {
  // Si nous sommes sur la page dashboard, charger les données par défaut
  if (document.querySelector(".tab-button")) {
    switchTab("nord")
  }
})
