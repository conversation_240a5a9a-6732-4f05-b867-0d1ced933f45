<?php
session_start();

// Configuration de la base de données (optionnel)
$host = 'localhost';
$dbname = 'ocp_kpi';
$username = 'root';
$password = '';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    
    // Validation email OCP
    if (!strpos($email, '@ocp')) {
        echo json_encode([
            'success' => false,
            'message' => 'L\'email doit contenir @ocp'
        ]);
        exit;
    }
    
    // Comptes de test
    $users = [
        '<EMAIL>' => [
            'password' => 'admin123',
            'role' => 'admin'
        ],
        '<EMAIL>' => [
            'password' => 'user123',
            'role' => 'user'
        ]
    ];
    
    // Vérification des identifiants
    if (isset($users[$email]) && $users[$email]['password'] === $password) {
        $_SESSION['user_email'] = $email;
        $_SESSION['user_role'] = $users[$email]['role'];
        
        $redirect_url = ($users[$email]['role'] === 'admin') ? 'admin.html' : 'dashboard.html';
        
        echo json_encode([
            'success' => true,
            'message' => 'Connexion réussie',
            'redirect' => $redirect_url
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Email ou mot de passe incorrect'
        ]);
    }
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Méthode non autorisée'
    ]);
}
?>
