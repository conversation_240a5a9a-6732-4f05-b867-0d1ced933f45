<?php
session_start();

// Vérifier si l'utilisateur est connecté et est admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    header('HTTP/1.1 403 Forbidden');
    echo json_encode(['success' => false, 'message' => 'Accès non autorisé']);
    exit;
}

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['excel_file'])) {
    $file = $_FILES['excel_file'];
    
    // Vérifier le type de fichier
    $allowed_types = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel'
    ];
    
    if (!in_array($file['type'], $allowed_types)) {
        echo json_encode([
            'success' => false,
            'message' => 'Type de fichier non autorisé. Utilisez .xlsx ou .xls'
        ]);
        exit;
    }
    
    // Créer le dossier uploads s'il n'existe pas
    $upload_dir = 'uploads/';
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }
    
    // Générer un nom unique pour le fichier
    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $new_filename = 'kpi_data_' . date('Y-m-d_H-i-s') . '.' . $file_extension;
    $upload_path = $upload_dir . $new_filename;
    
    // Déplacer le fichier uploadé
    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        // Ici vous pouvez traiter le fichier Excel
        // Pour cet exemple, nous simulons le traitement
        
        // Simuler le traitement des données
        sleep(2);
        
        // Sauvegarder les informations du fichier
        $file_info = [
            'filename' => $new_filename,
            'original_name' => $file['name'],
            'upload_time' => date('Y-m-d H:i:s'),
            'size' => $file['size']
        ];
        
        file_put_contents('data/last_upload.json', json_encode($file_info));
        
        echo json_encode([
            'success' => true,
            'message' => 'Fichier traité avec succès',
            'filename' => $new_filename
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Erreur lors de l\'upload du fichier'
        ]);
    }
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Aucun fichier reçu'
    ]);
}
?>
