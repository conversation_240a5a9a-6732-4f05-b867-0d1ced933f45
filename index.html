<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCP - Suivi KPI TSP</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="login-container">
        <!-- Background avec logo OCP -->
        <div class="background-logo"></div>
        
        <div class="login-card">
            <div class="logo-container">
                <div class="ocp-logo">OCP</div>
            </div>
            
            <h1>Suivi KPI TSP</h1>
            <p class="subtitle">Connectez-vous pour accéder au tableau de bord</p>
            
            <form id="loginForm" method="POST" action="login.php">
                <div class="form-group">
                    <label for="email">Email OCP</label>
                    <input type="email" id="email" name="email" placeholder="<EMAIL>" required>
                    <span class="error-message" id="emailError"></span>
                </div>
                
                <div class="form-group">
                    <label for="password">Mot de passe</label>
                    <div class="password-container">
                        <input type="password" id="password" name="password" placeholder="••••••••" required>
                        <button type="button" class="toggle-password" onclick="togglePassword()">
                            <span id="eyeIcon">👁️</span>
                        </button>
                    </div>
                </div>
                
                <div class="error-message" id="loginError"></div>
                
                <button type="submit" class="login-btn" id="loginBtn">
                    <span id="loginText">Se connecter</span>
                    <div class="spinner" id="spinner"></div>
                </button>
            </form>
            
            <div class="test-accounts">
                <p>Comptes de test :</p>
                <p>Admin: <EMAIL> / admin123</p>
                <p>Utilisateur: <EMAIL> / user123</p>
            </div>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
