* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  min-height: 100vh;
}

/* Page de connexion */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.background-logo {
  position: absolute;
  inset: 0;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><circle cx="100" cy="100" r="80" fill="%23059669" opacity="0.1"/><text x="100" y="110" text-anchor="middle" font-size="40" font-weight="bold" fill="%23059669" opacity="0.2">OCP</text></svg>');
  background-size: 400px 400px;
  background-position: center;
  background-repeat: no-repeat;
}

.login-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  width: 100%;
  max-width: 400px;
  position: relative;
  z-index: 10;
  border: 1px solid #bbf7d0;
}

.logo-container {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.ocp-logo {
  width: 80px;
  height: 80px;
  background: #059669;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 1.5rem;
}

h1 {
  text-align: center;
  color: #065f46;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
}

.subtitle {
  text-align: center;
  color: #059669;
  margin-bottom: 2rem;
  font-size: 0.9rem;
}

.form-group {
  margin-bottom: 1rem;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  color: #047857;
  font-weight: 500;
}

input[type="email"],
input[type="password"] {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #bbf7d0;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

input[type="email"]:focus,
input[type="password"]:focus {
  outline: none;
  border-color: #059669;
}

.password-container {
  position: relative;
}

.toggle-password {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
}

.error-message {
  color: #dc2626;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: none;
}

.error-message.show {
  display: block;
}

.login-btn {
  width: 100%;
  background: #059669;
  color: white;
  border: none;
  padding: 0.75rem;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-btn:hover {
  background: #047857;
}

.login-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #ffffff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  display: none;
  margin-left: 10px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.test-accounts {
  margin-top: 1.5rem;
  text-align: center;
  font-size: 0.8rem;
  color: #059669;
  background: #f0fdf4;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #bbf7d0;
}

/* Header commun */
.header {
  background: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid #bbf7d0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-logo {
  width: 40px;
  height: 40px;
  background: #059669;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
}

.header-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #065f46;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.btn {
  padding: 0.5rem 1rem;
  border: 1px solid #bbf7d0;
  background: transparent;
  color: #059669;
  border-radius: 6px;
  text-decoration: none;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn:hover {
  background: #f0fdf4;
}

.btn-primary {
  background: #059669;
  color: white;
  border-color: #059669;
}

.btn-primary:hover {
  background: #047857;
}

/* Dashboard */
.dashboard-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.tabs {
  margin-bottom: 2rem;
}

.tab-list {
  display: flex;
  background: #dcfce7;
  border-radius: 8px;
  padding: 4px;
}

.tab-button {
  flex: 1;
  padding: 0.75rem;
  background: transparent;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.tab-button.active {
  background: #059669;
  color: white;
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

.kpi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.kpi-card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #bbf7d0;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.kpi-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  color: #047857;
  font-size: 0.875rem;
  font-weight: 500;
}

.kpi-value {
  font-size: 2rem;
  font-weight: bold;
  color: #065f46;
  margin-bottom: 0.5rem;
}

.kpi-trend {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  color: #059669;
}

.chart-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.chart-card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #bbf7d0;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.chart-title {
  color: #065f46;
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.chart-description {
  color: #059669;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.chart-container {
  height: 300px;
  position: relative;
}

/* Admin page */
.admin-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
}

.upload-card {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  border: 1px solid #bbf7d0;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.file-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px dashed #bbf7d0;
  border-radius: 6px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.2s;
}

.file-input:hover {
  border-color: #059669;
}

.file-info {
  background: #f0fdf4;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #bbf7d0;
  margin: 1rem 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #bbf7d0;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 2rem;
  font-weight: bold;
  color: #059669;
}

.stat-label {
  font-size: 0.875rem;
  color: #059669;
}

/* Responsive */
@media (max-width: 768px) {
  .chart-grid {
    grid-template-columns: 1fr;
  }

  .kpi-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .login-card {
    margin: 1rem;
    padding: 1.5rem;
  }
}
